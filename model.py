from flask import Flask, request, jsonify
from flask_cors import CORS
import pickle
import pandas as pd
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG)

app = Flask(__name__)
CORS(app)

# Load trained churn model
try:
    with open('churn_model.pkl', 'rb') as f:
        model = pickle.load(f)
    logging.info("✅ Churn prediction model loaded successfully.")
except FileNotFoundError:
    logging.error("❌ Model file not found. Make sure 'churn_model.pkl' is in the correct directory.")
    exit()

@app.before_request
def log_request_info():
    logging.debug(f"\nIncoming {request.method} request to {request.path}")
    logging.debug("Headers: %s", request.headers)
    logging.debug("Body: %s", request.get_data())

@app.route('/')
def home():
    return "✅ Customer Churn Prediction API is running"

# 🔮 Churn Prediction Endpoint
@app.route('/predict', methods=['POST'])
def predict():
    data = request.json

    # Required features as used in training
    required_features = [
        'customer_tenure',
        'number_of_services_or_products',
        'average_monthly_usage',
        'days_since_last_interaction',
        'total_spent',
        'average_transaction_value',
        'outstanding_balance',
        'customer_support_calls',
        'payment_frequency',
        'discount_or_offer_received',
        'account_status'
    ]

    # Validate request
    missing = [field for field in required_features if field not in data]
    if missing:
        return jsonify({'error': f'Missing required fields: {missing}'}), 400

    try:
        # Convert input to DataFrame
        input_df = pd.DataFrame([data])
        logging.debug(f"\nReceived input:\n{input_df}")

        # Predict
        prediction = model.predict(input_df)
        probability = model.predict_proba(input_df)

        result = 'Churn' if prediction[0] == 1 else 'No Churn'
        probability_percent = probability[0][1] * 100

        return jsonify({
            'prediction': result,
            'probability': f"{probability_percent:.2f}%"
        })

    except ValueError as ve:
        logging.error(f"ValueError: {ve}")
        return jsonify({'error': 'Invalid input data format'}), 400
    except Exception as e:
        logging.error(f"Prediction error: {e}")
        return jsonify({'error': f'Internal server error: {e}'}), 500

# 📊 Churn Metrics Endpoint
@app.route('/metrics', methods=['GET'])
def get_metrics():
    try:
        # Load dataset
        df = pd.read_csv('//home//sufiyan//Desktop//ammad stuff//customer-churn//data.csv')

        # Ensure key columns are numeric
        numeric_cols = ['total_spent', 'number_of_services_or_products', 'churn_flag']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Drop rows with missing values
        df = df.dropna(subset=numeric_cols)

        # 📊 Total Spend
        total_spend = df['total_spent'].sum()

        # 🔁 Number of Returns (Purchases)
        total_purchases = df['number_of_services_or_products'].sum()

        # 📉 Churn %
        total_customers = len(df)
        churned_customers = df[df['churn_flag'] == 1].shape[0]
        churn_percentage = (churned_customers / total_customers) * 100

        # 🟢 Churn Zone
        if churn_percentage <= 25:
            churn_zone = "🟢 Green"
        elif churn_percentage <= 50:
            churn_zone = "🔵 Blue"
        elif churn_percentage <= 75:
            churn_zone = "🟠 Orange"
        else:
            churn_zone = "🔴 Red"

        return jsonify({
            "📊 Total Spend": round(total_spend, 2),
            "🔁 Number of Returns (Purchases/Subscribed Services)": int(total_purchases),
            "📉 Churn Percentage": f"{churn_percentage:.2f}%",
            "🟢 Churn Zone": churn_zone
        })

    except Exception as e:
        logging.error(f"Metrics Error: {e}")
        return jsonify({"error": "Failed to calculate metrics"}), 500

# Run the app
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
