from flask import Flask, request, jsonify
from flask_cors import CORS
import pickle
import pandas as pd
import logging

# Setup logging
logging.basicConfig(level=logging.DEBUG)

app = Flask(__name__)
CORS(app)

# Load trained model
try:
    with open('churn_model.pkl', 'rb') as f:
        model = pickle.load(f)
    logging.info("✅ Churn prediction model loaded successfully.")
except FileNotFoundError:
    logging.error("❌ Model file not found. Ensure 'churn_model.pkl' is in the correct directory.")
    exit()

@app.before_request
def log_request_info():
    logging.debug(f"\nIncoming {request.method} request to {request.path}")
    logging.debug("Headers: %s", request.headers)
    logging.debug("Body: %s", request.get_data())

@app.route('/')
def home():
    return "✅ Customer Churn Prediction API is running"

@app.route('/predict', methods=['POST'])
def predict():
    data = request.json

    # Model expects the following features:
    required_features = [
        'customer_tenure',
        'number_of_services_or_products',
        'average_monthly_usage',
        'days_since_last_interaction',
        'total_spent',
        'average_transaction_value',
        'outstanding_balance',
        'customer_support_calls',
        'payment_frequency',
        'discount_or_offer_received',
        'account_status'
    ]

    # Check for missing fields
    missing = [field for field in required_features if field not in data]
    if missing:
        return jsonify({'error': f'Missing required fields: {missing}'}), 400

    try:
        # Convert incoming data to DataFrame
        input_df = pd.DataFrame([data])
        logging.debug(f"\nReceived input data:\n{input_df}")

        # Make prediction
        prediction = model.predict(input_df)
        probability = model.predict_proba(input_df)

        result = 'Churn' if prediction[0] == 1 else 'No Churn'
        probability_percent = probability[0][1] * 100

        return jsonify({
            'prediction': result,
            'probability': f"{probability_percent:.2f}%"
        })

    except ValueError as ve:
        logging.error(f"❗ValueError: {ve}")
        return jsonify({'error': 'Invalid input data format'}), 400
    except Exception as e:
        logging.error(f"❗Prediction error: {e}")
        return jsonify({'error': f'Internal server error: {e}'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
