<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Customer <PERSON>rn Prediction</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: #333;
    }

    .container {
      background: white;
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      max-width: 800px;
      margin: 0 auto;
    }

    input, select {
      margin: 5px;
      padding: 12px;
      width: 250px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s;
    }

    input:focus, select:focus {
      outline: none;
      border-color: #667eea;
    }

    button {
      padding: 12px 24px;
      margin-top: 10px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: transform 0.2s, box-shadow 0.2s;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .result {
      margin-top: 20px;
      padding: 20px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-left: 5px solid #667eea;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .metrics-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }

    .metric-card {
      background: white;
      padding: 20px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: transform 0.3s;
    }

    .metric-card:hover {
      transform: translateY(-5px);
    }

    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
      margin: 10px 0;
    }

    .metric-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .churn-zone {
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: bold;
      display: inline-block;
    }

    .zone-green {
      background: #d4edda;
      color: #155724;
    }

    .zone-blue {
      background: #cce7ff;
      color: #004085;
    }

    .zone-orange {
      background: #fff3cd;
      color: #856404;
    }

    .zone-red {
      background: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>🔮 Customer Churn Predictor</h2>

    <form id="predictForm">
      <input type="number" name="customer_tenure" placeholder="Customer Tenure" required><br>
      <input type="number" name="number_of_services_or_products" placeholder="Number of Services" required><br>
      <input type="number" name="average_monthly_usage" placeholder="Average Monthly Usage" required><br>
      <input type="number" name="days_since_last_interaction" placeholder="Days Since Last Interaction" required><br>
      <input type="number" name="total_spent" placeholder="Total Spent" required><br>
      <input type="number" name="average_transaction_value" placeholder="Average Transaction Value" required><br>
      <input type="number" name="outstanding_balance" placeholder="Outstanding Balance" required><br>
      <input type="number" name="customer_support_calls" placeholder="Customer Support Calls" required><br>

      <select name="payment_frequency" required>
        <option value="">Select Payment Frequency</option>
        <option value="Monthly">Monthly</option>
        <option value="Quarterly">Quarterly</option>
        <option value="Yearly">Yearly</option>
      </select><br>

      <select name="discount_or_offer_received" required>
        <option value="">Discount/Offer Received?</option>
        <option value="Yes">Yes</option>
        <option value="No">No</option>
      </select><br>

      <select name="account_status" required>
        <option value="">Account Status</option>
        <option value="Active">Active</option>
        <option value="Inactive">Inactive</option>
        <option value="Suspended">Suspended</option>
      </select><br>

      <button type="submit">Predict Churn</button>
    </form>

    <div id="predictResult" class="result"></div>

    <h3>📊 System Metrics</h3>
    <div id="metricsResult" class="result"></div>
  </div>

  <script>
    // Predict Churn and Load Metrics Together
    document.getElementById('predictForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      const formData = new FormData(this);
      const data = {};
      formData.forEach((value, key) => data[key] = isNaN(value) ? value : parseFloat(value));

      try {
        // Show loading message
        document.getElementById('predictResult').innerHTML = '<div style="text-align: center;">🔄 Loading prediction...</div>';
        document.getElementById('metricsResult').innerHTML = '<div style="text-align: center;">🔄 Loading metrics...</div>';

        // Get prediction and metrics simultaneously
        const [predictionResponse, metricsResponse] = await Promise.all([
          fetch('http://localhost:5000/predict', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          }),
          fetch('http://localhost:5000/metrics')
        ]);

        const predictionResult = await predictionResponse.json();
        const metrics = await metricsResponse.json();

        // Display prediction result
        document.getElementById('predictResult').innerHTML = `
          <div style="text-align: center;">
            <h4>🔮 Prediction Result</h4>
            <div style="font-size: 20px; margin: 10px 0;">
              <strong>Prediction:</strong> <span style="color: ${predictionResult.prediction === 'Churn' ? '#dc3545' : '#28a745'};">${predictionResult.prediction}</span>
            </div>
            <div style="font-size: 18px;">
              <strong>Probability:</strong> ${predictionResult.probability}
            </div>
          </div>
        `;

        // Display metrics with beautiful cards
        const churnZone = metrics["🟢 Churn Zone"];
        let zoneClass = 'zone-green';
        if (churnZone.includes('🔵')) zoneClass = 'zone-blue';
        else if (churnZone.includes('🟠')) zoneClass = 'zone-orange';
        else if (churnZone.includes('🔴')) zoneClass = 'zone-red';

        document.getElementById('metricsResult').innerHTML = `
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-label">📊 Total Spend</div>
              <div class="metric-value">$${metrics["📊 Total Spend"].toLocaleString()}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">🔁 Number of Returns</div>
              <div class="metric-value">${metrics["🔁 Number of Returns (Purchases/Subscribed Services)"].toLocaleString()}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">📉 Churn Percentage</div>
              <div class="metric-value">${metrics["📉 Churn Percentage"]}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">🟢 Churn Zone</div>
              <div class="metric-value">
                <span class="churn-zone ${zoneClass}">${metrics["🟢 Churn Zone"]}</span>
              </div>
            </div>
          </div>
        `;

      } catch (error) {
        document.getElementById('predictResult').innerHTML = `
          <div style="color: red; text-align: center;">
            ❌ Error: ${error.message}
          </div>
        `;
        document.getElementById('metricsResult').innerHTML = `
          <div style="color: red; text-align: center;">
            ❌ Error loading metrics: Make sure Flask server is running
          </div>
        `;
      }
    });

    // Optional: Load metrics only (if needed separately)
    async function loadMetrics() {
      try {
        const res = await fetch('http://localhost:5000/metrics');
        const metrics = await res.json();

        const churnZone = metrics["🟢 Churn Zone"];
        let zoneClass = 'zone-green';
        if (churnZone.includes('🔵')) zoneClass = 'zone-blue';
        else if (churnZone.includes('🟠')) zoneClass = 'zone-orange';
        else if (churnZone.includes('🔴')) zoneClass = 'zone-red';

        document.getElementById('metricsResult').innerHTML = `
          <div class="metrics-grid">
            <div class="metric-card">
              <div class="metric-label">📊 Total Spend</div>
              <div class="metric-value">$${metrics["📊 Total Spend"].toLocaleString()}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">🔁 Number of Returns</div>
              <div class="metric-value">${metrics["🔁 Number of Returns (Purchases/Subscribed Services)"].toLocaleString()}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">📉 Churn Percentage</div>
              <div class="metric-value">${metrics["📉 Churn Percentage"]}</div>
            </div>

            <div class="metric-card">
              <div class="metric-label">🟢 Churn Zone</div>
              <div class="metric-value">
                <span class="churn-zone ${zoneClass}">${metrics["🟢 Churn Zone"]}</span>
              </div>
            </div>
          </div>
        `;
      } catch (error) {
        document.getElementById('metricsResult').innerHTML = `
          <div style="color: red; text-align: center;">
            ❌ Error loading metrics: ${error.message}
          </div>
        `;
      }
    }
  </script>

</body>
</html>
