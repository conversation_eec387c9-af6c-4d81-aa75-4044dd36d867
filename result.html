<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Customer Churn Prediction</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 40px;
    }
    input, select {
      margin: 5px;
      padding: 8px;
      width: 250px;
    }
    button {
      padding: 10px 20px;
      margin-top: 10px;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      background: #f4f4f4;
      border-left: 5px solid #333;
    }
  </style>
</head>
<body>

  <h2>🔮 Customer Churn Predictor</h2>

  <form id="predictForm">
    <input type="number" name="customer_tenure" placeholder="Customer Tenure" required><br>
    <input type="number" name="number_of_services_or_products" placeholder="Number of Services" required><br>
    <input type="number" name="average_monthly_usage" placeholder="Average Monthly Usage" required><br>
    <input type="number" name="days_since_last_interaction" placeholder="Days Since Last Interaction" required><br>
    <input type="number" name="total_spent" placeholder="Total Spent" required><br>
    <input type="number" name="average_transaction_value" placeholder="Average Transaction Value" required><br>
    <input type="number" name="outstanding_balance" placeholder="Outstanding Balance" required><br>
    <input type="number" name="customer_support_calls" placeholder="Customer Support Calls" required><br>
    
    <select name="payment_frequency" required>
      <option value="">Select Payment Frequency</option>
      <option value="Monthly">Monthly</option>
      <option value="Quarterly">Quarterly</option>
      <option value="Yearly">Yearly</option>
    </select><br>

    <select name="discount_or_offer_received" required>
      <option value="">Discount/Offer Received?</option>
      <option value="Yes">Yes</option>
      <option value="No">No</option>
    </select><br>

    <select name="account_status" required>
      <option value="">Account Status</option>
      <option value="Active">Active</option>
      <option value="Inactive">Inactive</option>
      <option value="Suspended">Suspended</option>
    </select><br>

    <button type="submit">Predict Churn</button>
  </form>

  <div id="predictResult" class="result"></div>

  <h3>📊 System Metrics</h3>
  <button onclick="loadMetrics()">Load Churn Metrics</button>
  <div id="metricsResult" class="result"></div>

  <script>
    // Predict Churn
    document.getElementById('predictForm').addEventListener('submit', async function (e) {
      e.preventDefault();
      const formData = new FormData(this);
      const data = {};
      formData.forEach((value, key) => data[key] = isNaN(value) ? value : parseFloat(value));

      const response = await fetch('http://localhost:5000/predict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      document.getElementById('predictResult').innerHTML = `
        <strong>Prediction:</strong> ${result.prediction}<br>
        <strong>Probability:</strong> ${result.probability}
      `;
    });

    // Load Metrics
    async function loadMetrics() {
      const res = await fetch('http://localhost:5000/metrics');
      const metrics = await res.json();
      document.getElementById('metricsResult').innerHTML = `
        <strong>📊 Total Spend:</strong> $${metrics["📊 Total Spend"]}<br>
        <strong>🔁 Number of Purchases:</strong> ${metrics["🔁 Number of Returns (Purchases/Subscribed Services)"]}<br>
        <strong>📉 Churn Percentage:</strong> ${metrics["📉 Churn Percentage"]}<br>
        <strong>🟢 Churn Zone:</strong> ${metrics["🟢 Churn Zone"]}
      `;
    }
  </script>

</body>
</html>
